@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

@theme {
  --color-moon-navy: #1a1f3a;
  --color-moon-silver: #c0c5d0;
  --color-moon-silver-dark: #8a8f9a;
  --color-moon-white: #fafbfc;
  --color-moon-gold: #d4af37;

  --font-family-serif: 'Playfair Display', serif;
  --font-family-sans: 'Inter', sans-serif;
}

/* Accessibility and base styles */
*,
*::before,
*::after {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

/* Respect user's motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid var(--color-moon-gold);
  outline-offset: 2px;
}

/* Skip to main content link */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-moon-navy);
  color: var(--color-moon-white);
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
}

.skip-link:focus {
  top: 6px;
}

/* Custom component styles with improved accessibility */
.btn-primary {
  background-color: var(--color-moon-navy);
  color: var(--color-moon-white);
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 2px solid var(--color-moon-navy);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 44px; /* WCAG minimum touch target */
  text-decoration: none;
}

.btn-primary:hover,
.btn-primary:focus {
  opacity: 0.9;
  transform: translateY(-1px);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.btn-secondary {
  background-color: var(--color-moon-silver);
  color: var(--color-moon-navy);
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  border: 2px solid var(--color-moon-silver);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 44px;
  text-decoration: none;
}

.btn-secondary:hover,
.btn-secondary:focus {
  opacity: 0.9;
  transform: translateY(-1px);
}

.btn-secondary:active {
  transform: translateY(0);
}

.btn-secondary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.btn-outline {
  border: 2px solid var(--color-moon-navy);
  color: var(--color-moon-navy);
  background: transparent;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-height: 44px;
  text-decoration: none;
}

.btn-outline:hover,
.btn-outline:focus {
  background-color: var(--color-moon-navy);
  color: var(--color-moon-white);
  transform: translateY(-1px);
}

.btn-outline:active {
  transform: translateY(0);
}

.btn-outline:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Responsive spacing and layout */
.section-padding {
  padding: 2rem 1rem;
}

@media (min-width: 480px) {
  .section-padding {
    padding: 3rem 1rem;
  }
}

@media (min-width: 640px) {
  .section-padding {
    padding: 4rem 1.5rem;
  }
}

@media (min-width: 1024px) {
  .section-padding {
    padding: 6rem 2rem;
  }
}

.container-max {
  max-width: 80rem;
  margin: 0 auto;
  width: 100%;
}

/* Improved form accessibility */
input, textarea, select {
  font-size: 16px; /* Prevents zoom on iOS */
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .btn-primary,
  .btn-secondary,
  .btn-outline {
    border-width: 3px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  :root {
    --color-moon-navy: #0f1419;
    --color-moon-silver: #8a8f9a;
    --color-moon-silver-dark: #6a6f7a;
    --color-moon-white: #ffffff;
    --color-moon-gold: #e4bf47;
  }

  /* Ensure body has proper dark background */
  body {
    background-color: #1a1f2e;
    color: #ffffff;
  }
}

/* Print styles */
@media print {
  .btn-primary,
  .btn-secondary,
  .btn-outline {
    background: transparent !important;
    color: black !important;
    border: 2px solid black !important;
  }

  video {
    display: none;
  }

  .section-padding {
    padding: 1rem 0;
  }
}

/* Screen reader only content */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Custom gradients */
.bg-gradient-moon {
  background: linear-gradient(135deg, var(--color-moon-navy) 0%, #2a2f4a 100%);
}

/* Mobile-first responsive utilities */
.mobile-menu-overlay {
  position: fixed;
  inset: 0;
  background-color: rgba(26, 31, 58, 0.8);
  backdrop-filter: blur(4px);
  z-index: 40;
}

/* Touch-friendly spacing */
@media (max-width: 767px) {
  .mobile-spacing {
    padding: 1rem;
  }

  .mobile-text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .mobile-text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }
}
